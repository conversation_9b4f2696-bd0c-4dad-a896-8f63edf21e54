2025/09/01 14:08:57.119414 config.go:55: open E:\BaseOS\NetWork\Gateway\Configs\Gateway.json: The system cannot find the path specified.
2025/09/01 14:10:10.926889 service_main.go:109: Starting traffic monitoring system...
2025/09/01 14:10:10.926889 db.go:36: Failed to connect to database: SQLite support is disabled (requires CGO), database will be disabled
2025/09/01 14:10:10.927544 monitor_dao.go:39: Database connection is nil, monitor will work in memory-only mode
2025/09/01 14:10:10.927544 integration_example.go:195: Traffic monitoring system started
2025/09/01 14:10:10.927544 service_main.go:111: Traffic monitoring system started successfully
2025/09/01 14:10:10.927544 intranet_tunnel_dao.go:38: Database connection is nil, intranet tunnel DAO will work in memory-only mode
2025/09/01 14:10:10.930062 service_main.go:121: HTTP API server listening on port 8080
2025/09/01 14:10:10.930062 service_main.go:122: TCP Proxy server listening on port 8081
2025/09/01 14:10:10.930062 service_main.go:123: Server configuration: Port range 10000-20000, Max connections 1000, Timeout 30s
2025/09/01 14:10:10.930062 service_main.go:125: Port cache expiration: 168h0m0s
2025/09/01 14:10:10.930062 cleaner.go:57: start cache cleaner, clean interval: 84h0m0s, expiration: 168h0m0s
2025/09/01 14:10:10.930062 intranet_tunnel_dao.go:93: Database connection is nil, returning empty tunnel list
2025/09/01 14:10:27.988952 service_main.go:156: Received shutdown signal, shutting down...
2025/09/01 14:10:27.988952 service_main.go:172: Shutting down service...
2025/09/01 14:10:27.989459 service_main.go:182: HTTP server shutdown successfully
2025/09/01 14:10:27.989459 service_main.go:189: Service shutdown completed
2025/09/01 14:19:33.386447 service_main.go:109: Starting traffic monitoring system...
2025/09/01 14:19:33.516341 integration_example.go:195: Traffic monitoring system started
2025/09/01 14:19:33.517296 service_main.go:111: Traffic monitoring system started successfully
2025/09/01 14:19:33.584526 service_main.go:121: HTTP API server listening on port 8090
2025/09/01 14:19:33.585098 service_main.go:122: TCP Proxy server listening on port 8091
2025/09/01 14:19:33.585098 service_main.go:123: Server configuration: Port range 60000-62000, Max connections 50, Timeout 100s
2025/09/01 14:19:33.585098 service_main.go:125: Port cache expiration: 1440h0m0s
2025/09/01 14:19:33.585098 cleaner.go:57: start cache cleaner, clean interval: 720h0m0s, expiration: 1440h0m0s
2025/09/01 14:19:33.587269 conn.go:236: recover from db, url path: [{"service_name":"FakeService","service_group":"BaseOS.AI.Fundry","service_port":"6002","base_url":"/"}], client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:19:33.590546 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /
2025/09/01 14:19:33.590546 conn.go:239: recover from db, server port: 60002, client port: localhost:22, client: 3fc0c44f-6b02-384f-8539-251a39f8b040
2025/09/01 14:19:33.593427 monitor_service.go:64: TCP monitor already exists for client 3fc0c44f-6b02-384f-8539-251a39f8b040, port 60002
2025/09/01 14:19:33.593427 conn.go:239: recover from db, server port: 60001, client port: localhost:22, client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:19:33.595033 monitor_service.go:64: TCP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, port 60001
2025/09/01 14:19:33.595033 conn.go:239: recover from db, server port: 60000, client port: localhost:8000, client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:19:33.595628 monitor_service.go:64: TCP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, port 60000
2025/09/01 14:19:33.596661 conn.go:236: recover from db, url path: [{"service_name":"testService","service_group":"TEST","service_port":"localhost:8000","base_url":"/"},{"service_name":"testService","service_group":"TEST","service_port":"localhost:8000","base_url":"/time"}], client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:19:33.598139 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /
2025/09/01 14:19:33.598687 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /time
2025/09/01 14:19:33.599645 conn.go:236: recover from db, url path: [{"service_name":"TESTSERVICE","service_group":"TEST","service_port":"localhost:8000","base_url":"/"}], client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:19:33.600988 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /
2025/09/01 14:19:49.178629 service_main.go:156: Received shutdown signal, shutting down...
2025/09/01 14:19:49.178629 service_main.go:172: Shutting down service...
2025/09/01 14:19:49.178629 service_main.go:182: HTTP server shutdown successfully
2025/09/01 14:19:49.178629 service_main.go:189: Service shutdown completed
2025/09/01 14:21:12.484521 service_main.go:119: Starting traffic monitoring system...
2025/09/01 14:21:12.590607 integration_example.go:195: Traffic monitoring system started
2025/09/01 14:21:12.591501 service_main.go:121: Traffic monitoring system started successfully
2025/09/01 14:21:12.652173 service_main.go:131: HTTP API server listening on port 8090
2025/09/01 14:21:12.652766 service_main.go:132: TCP Proxy server listening on port 8091
2025/09/01 14:21:12.652766 service_main.go:133: Server configuration: Port range 60000-62000, Max connections 50, Timeout 100s
2025/09/01 14:21:12.652766 service_main.go:135: Port cache expiration: 1440h0m0s
2025/09/01 14:21:12.652766 cleaner.go:57: start cache cleaner, clean interval: 720h0m0s, expiration: 1440h0m0s
2025/09/01 14:21:12.655527 conn.go:236: recover from db, url path: [{"service_name":"FakeService","service_group":"BaseOS.AI.Fundry","service_port":"6002","base_url":"/"}], client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:21:12.657683 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /
2025/09/01 14:21:12.658510 conn.go:239: recover from db, server port: 60002, client port: localhost:22, client: 3fc0c44f-6b02-384f-8539-251a39f8b040
2025/09/01 14:21:12.661245 monitor_service.go:64: TCP monitor already exists for client 3fc0c44f-6b02-384f-8539-251a39f8b040, port 60002
2025/09/01 14:21:12.661245 conn.go:239: recover from db, server port: 60001, client port: localhost:22, client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:21:12.662108 monitor_service.go:64: TCP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, port 60001
2025/09/01 14:21:12.663066 conn.go:239: recover from db, server port: 60000, client port: localhost:8000, client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:21:12.664720 monitor_service.go:64: TCP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, port 60000
2025/09/01 14:21:12.664720 conn.go:236: recover from db, url path: [{"service_name":"testService","service_group":"TEST","service_port":"localhost:8000","base_url":"/"},{"service_name":"testService","service_group":"TEST","service_port":"localhost:8000","base_url":"/time"}], client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:21:12.666387 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /
2025/09/01 14:21:12.667742 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /time
2025/09/01 14:21:12.668109 conn.go:236: recover from db, url path: [{"service_name":"TESTSERVICE","service_group":"TEST","service_port":"localhost:8000","base_url":"/"}], client: a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8
2025/09/01 14:21:12.668925 monitor_service.go:42: HTTP monitor already exists for client a63fd3ab-ba26-38bc-a8aa-bb7ba32a02d8, route /
2025/09/01 14:21:28.045266 service_main.go:172: Received shutdown signal, shutting down...
2025/09/01 14:21:28.045266 service_main.go:189: Shutting down service...
2025/09/01 14:21:28.045266 service_main.go:199: HTTP server shutdown successfully
2025/09/01 14:21:28.045266 service_main.go:206: Service shutdown completed
