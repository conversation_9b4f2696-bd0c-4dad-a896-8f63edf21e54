package dao

import (
	"log"
	"sync"
	"time"

	"gorm.io/gorm"
	"socks/server/monitor/domain/entity"
	"socks/server/monitor/domain/repo"
	infraRepo "socks/server/monitor/infra/repo"
	"socks/server/util"
)

var (
	monitorDao *MonitorDaoImpl
	mdOnce     sync.Once
)

type MonitorDaoImpl struct {
	db *gorm.DB
}

// GetMonitorDaoImpl 获取监控DAO实现实例
func GetMonitorDaoImpl() *MonitorDaoImpl {
	mdOnce.Do(func() {
		db := util.GetDB()
		monitorDao = &MonitorDaoImpl{
			db: db,
		}

		// 如果数据库连接失败，跳过迁移
		if db != nil {
			err := monitorDao.db.AutoMigrate(&infraRepo.TunnelMonitorModel{})
			if err != nil {
				log.Printf("auto migrate tunnel monitor table fail, err: %v", err)
			}
		} else {
			log.Println("Database connection is nil, monitor will work in memory-only mode")
		}
	})

	return monitorDao
}

// isDBAvailable 检查数据库连接是否可用
func (md *MonitorDaoImpl) isDBAvailable() bool {
	if md.db == nil {
		log.Println("Database connection is nil, operation skipped")
		return false
	}
	return true
}

// Create 创建新的监控记录
func (md *MonitorDaoImpl) Create(monitor *entity.TunnelMonitor) error {
	if !md.isDBAvailable() {
		return nil
	}

	var model infraRepo.TunnelMonitorModel
	model.FromEntity(monitor)
	err := md.db.Create(&model).Error
	if err == nil {
		monitor.ID = model.ID
	}
	return err
}

// GetByID 根据ID获取监控记录
func (md *MonitorDaoImpl) GetByID(id int) (*entity.TunnelMonitor, error) {
	if !md.isDBAvailable() {
		return nil, nil
	}

	var model infraRepo.TunnelMonitorModel
	err := md.db.First(&model, id).Error
	if err != nil {
		return nil, err
	}
	return model.ToEntity(), nil
}

// GetByHTTPRoute 根据HTTP路由获取监控记录
func (md *MonitorDaoImpl) GetByHTTPRoute(clientUUID, serverRoute string) (*entity.TunnelMonitor, error) {
	if !md.isDBAvailable() {
		return nil, nil
	}

	var model infraRepo.TunnelMonitorModel
	err := md.db.Where("client_uuid = ? AND server_route = ? AND tunnel_type = ?",
		clientUUID, serverRoute, string(entity.TunnelTypeHTTP)).First(&model).Error
	if err != nil {
		return nil, err
	}
	return model.ToEntity(), nil
}

// GetByTCPPort 根据TCP端口获取监控记录
func (md *MonitorDaoImpl) GetByTCPPort(clientUUID string, serverPort int) (*entity.TunnelMonitor, error) {
	if !md.isDBAvailable() {
		return nil, nil
	}

	var model infraRepo.TunnelMonitorModel
	err := md.db.Where("client_uuid = ? AND server_port = ? AND tunnel_type = ?",
		clientUUID, serverPort, string(entity.TunnelTypeTCP)).First(&model).Error
	if err != nil {
		return nil, err
	}
	return model.ToEntity(), nil
}

// GetByClientUUID 根据客户端UUID获取所有监控记录
func (md *MonitorDaoImpl) GetByClientUUID(clientUUID string) ([]*entity.TunnelMonitor, error) {
	if !md.isDBAvailable() {
		return []*entity.TunnelMonitor{}, nil
	}

	var models []infraRepo.TunnelMonitorModel
	err := md.db.Where("client_uuid = ?", clientUUID).Find(&models).Error
	if err != nil {
		return nil, err
	}

	var monitors []*entity.TunnelMonitor
	for _, model := range models {
		monitors = append(monitors, model.ToEntity())
	}
	return monitors, nil
}

// GetByClientGroup 根据客户端组获取所有监控记录
func (md *MonitorDaoImpl) GetByClientGroup(clientGroup string) ([]*entity.TunnelMonitor, error) {
	var models []infraRepo.TunnelMonitorModel
	err := md.db.Where("client_group = ?", clientGroup).Find(&models).Error
	if err != nil {
		return nil, err
	}

	var monitors []*entity.TunnelMonitor
	for _, model := range models {
		monitors = append(monitors, model.ToEntity())
	}
	return monitors, nil
}

// GetByTunnelType 根据隧道类型获取所有监控记录
func (md *MonitorDaoImpl) GetByTunnelType(tunnelType entity.TunnelType) ([]*entity.TunnelMonitor, error) {
	var models []infraRepo.TunnelMonitorModel
	err := md.db.Where("tunnel_type = ?", string(tunnelType)).Find(&models).Error
	if err != nil {
		return nil, err
	}

	var monitors []*entity.TunnelMonitor
	for _, model := range models {
		monitors = append(monitors, model.ToEntity())
	}
	return monitors, nil
}

// GetAll 获取所有监控记录
func (md *MonitorDaoImpl) GetAll() ([]*entity.TunnelMonitor, error) {
	var models []infraRepo.TunnelMonitorModel
	err := md.db.Find(&models).Error
	if err != nil {
		return nil, err
	}

	var monitors []*entity.TunnelMonitor
	for _, model := range models {
		monitors = append(monitors, model.ToEntity())
	}
	return monitors, nil
}

// Update 更新监控记录
func (md *MonitorDaoImpl) Update(monitor *entity.TunnelMonitor) error {
	if !md.isDBAvailable() {
		return nil
	}

	var model infraRepo.TunnelMonitorModel
	model.FromEntity(monitor)

	return md.db.Model(&model).Where("id = ?", model.ID).Updates(map[string]interface{}{
		"server_route": model.ServerRoute,
		"server_port":  model.ServerPort,
		"traffic_data": model.TrafficData,
		"client_group": model.ClientGroup,
		"tunnel_type":  model.TunnelType,
		"client_uuid":  model.ClientUUID,
		"client_ip":    model.ClientIP,
		"client_name":  model.ClientName,
		"service_name": model.ServiceName,
		"update_time":  time.Now(),
	}).Error
}

// UpdateTrafficData 更新流量数据
func (md *MonitorDaoImpl) UpdateTrafficData(id int, trafficData entity.TrafficDataList) error {
	if !md.isDBAvailable() {
		return nil
	}

	var model infraRepo.TrafficDataListModel
	model.FromEntity(trafficData)

	return md.db.Model(&infraRepo.TunnelMonitorModel{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"traffic_data": model,
			"update_time":  time.Now(),
		}).Error
}

// Delete 删除监控记录
func (md *MonitorDaoImpl) Delete(id int) error {
	return md.db.Delete(&infraRepo.TunnelMonitorModel{}, id).Error
}

// DeleteByClientUUID 删除指定客户端的所有监控记录
func (md *MonitorDaoImpl) DeleteByClientUUID(clientUUID string) error {
	return md.db.Where("client_uuid = ?", clientUUID).Delete(&infraRepo.TunnelMonitorModel{}).Error
}

// AddTrafficData 添加流量数据点
func (md *MonitorDaoImpl) AddTrafficData(clientUUID, serverRoute string, serverPort int, tunnelType entity.TunnelType, data *entity.TrafficData) error {
	var model infraRepo.TunnelMonitorModel
	var err error

	// 根据隧道类型查找监控记录
	if tunnelType == entity.TunnelTypeHTTP {
		err = md.db.Where("client_uuid = ? AND server_route = ? AND tunnel_type = ?",
			clientUUID, serverRoute, string(tunnelType)).First(&model).Error
	} else {
		err = md.db.Where("client_uuid = ? AND server_port = ? AND tunnel_type = ?",
			clientUUID, serverPort, string(tunnelType)).First(&model).Error
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 记录不存在，创建新记录
			model = infraRepo.TunnelMonitorModel{
				ClientUUID: clientUUID,
				TunnelType: string(tunnelType),
				UpdateTime: time.Now(),
			}
			model.AddTrafficData(data)

			if tunnelType == entity.TunnelTypeHTTP {
				model.ServerRoute = serverRoute
			} else {
				model.ServerPort = serverPort
			}
			return md.db.Create(&model).Error
		}
		return err
	}

	// 添加流量数据
	model.AddTrafficData(data)

	// 更新数据库
	monitor := model.ToEntity()
	return md.Update(monitor)
}

// GetTrafficDataInRange 获取指定时间范围内的流量数据
func (md *MonitorDaoImpl) GetTrafficDataInRange(clientUUID string, start, end time.Time) ([]*entity.TunnelMonitor, error) {
	var models []infraRepo.TunnelMonitorModel
	err := md.db.Where("client_uuid = ? AND update_time >= ? AND update_time <= ?",
		clientUUID, start, end).Find(&models).Error
	if err != nil {
		return nil, err
	}

	var monitors []*entity.TunnelMonitor
	for _, model := range models {
		monitors = append(monitors, model.ToEntity())
	}
	return monitors, nil
}

// CleanOldData 清理超过指定时间的旧数据
func (md *MonitorDaoImpl) CleanOldData(olderThan time.Time) error {
	return md.db.Where("update_time < ?", olderThan).Delete(&infraRepo.TunnelMonitorModel{}).Error
}

// GetActiveMonitors 获取活跃的监控记录（最近有流量数据的）
func (md *MonitorDaoImpl) GetActiveMonitors(since time.Time) ([]*entity.TunnelMonitor, error) {
	var models []infraRepo.TunnelMonitorModel
	err := md.db.Where("update_time >= ?", since).Find(&models).Error
	if err != nil {
		return nil, err
	}

	var monitors []*entity.TunnelMonitor
	for _, model := range models {
		monitors = append(monitors, model.ToEntity())
	}
	return monitors, nil
}

// BatchUpdateTrafficData 批量更新流量数据
func (md *MonitorDaoImpl) BatchUpdateTrafficData(updates []repo.TrafficDataUpdate) error {
	if len(updates) == 0 {
		return nil
	}

	return md.db.Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			var model infraRepo.TrafficDataListModel
			model.FromEntity(update.TrafficData)

			err := tx.Model(&infraRepo.TunnelMonitorModel{}).
				Where("id = ?", update.ID).
				Updates(map[string]interface{}{
					"traffic_data": model,
					"update_time":  time.Now(),
				}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
