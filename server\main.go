package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"runtime"

	"golang.org/x/sys/windows/svc"
	"socks/server/service"
)

func main() {
	// 设置详细日志
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)

	// 解析命令行参数
	var (
		installFlag   = flag.Bool("install", false, "安装Windows服务")
		uninstallFlag = flag.Bool("uninstall", false, "卸载Windows服务")
		startFlag     = flag.Bool("start", false, "启动Windows服务")
		stopFlag      = flag.Bool("stop", false, "停止Windows服务")
		serviceFlag   = flag.Bool("service", false, "以服务模式运行")
		debugFlag     = flag.Bool("debug", false, "调试模式运行服务")
		configFlag    = flag.String("config", "", "配置文件路径")
		cacheFlag     = flag.Int("cache", 0, "端口缓存过期时间(分钟)")
	)
	flag.Parse()

	// 检查是否在Windows系统上运行
	if runtime.GOOS != "windows" {
		log.Fatal("This service is only supported on Windows")
	}

	// 处理服务管理命令
	if *installFlag {
		err := service.InstallService()
		if err != nil {
			log.Fatalf("Failed to install service: %v", err)
		}
		fmt.Println("Service installed successfully")
		return
	}

	if *uninstallFlag {
		err := service.RemoveService()
		if err != nil {
			log.Fatalf("Failed to uninstall service: %v", err)
		}
		fmt.Println("Service uninstalled successfully")
		return
	}

	if *startFlag {
		err := service.StartService()
		if err != nil {
			log.Fatalf("Failed to start service: %v", err)
		}
		fmt.Println("Service started successfully")
		return
	}

	if *stopFlag {
		err := service.StopService()
		if err != nil {
			log.Fatalf("Failed to stop service: %v", err)
		}
		fmt.Println("Service stopped successfully")
		return
	}

	// 检查是否以服务模式运行
	isIntSess, err := svc.IsAnInteractiveSession()
	if err != nil {
		log.Fatalf("Failed to determine if we are running in an interactive session: %v", err)
	}

	if !isIntSess || *serviceFlag {
		// 以Windows服务模式运行
		err := service.RunService(*debugFlag)
		if err != nil {
			log.Fatalf("Failed to run service: %v", err)
		}
		return
	}

	// 以普通模式运行（用于开发和调试）
	log.Println("Running in console mode (for development/debugging)")
	log.Println("Use -install to install as Windows service")

	// 创建一个上下文用于普通模式
	ctx := context.Background()

	// 构建参数列表，包含配置参数
	args := []string{os.Args[0]} // 程序名
	if *configFlag != "" {
		args = append(args, "-config", *configFlag)
	}
	if *cacheFlag != 0 {
		args = append(args, "-cache", fmt.Sprintf("%d", *cacheFlag))
	}

	err = service.StartMainService(ctx, args)
	if err != nil {
		log.Fatalf("Failed to start main service: %v", err)
	}
}
