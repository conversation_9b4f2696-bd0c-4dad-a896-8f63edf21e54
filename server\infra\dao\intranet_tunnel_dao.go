package dao

import (
	"fmt"
	"log"
	"sync"
	"time"

	repo "socks/server/infra/repo"
	util "socks/server/util"

	"gorm.io/gorm"
)

var (
	dao  *IntranetTunnelDaoImpl
	once sync.Once
)

type IntranetTunnelDaoImpl struct {
	db *gorm.DB
}

func GetIntranetTunnelDaoImpl() *IntranetTunnelDaoImpl {
	once.Do(func() {
		db := util.GetDB()
		dao = &IntranetTunnelDaoImpl{
			db: db,
		}

		// 如果数据库连接失败，跳过迁移
		if db != nil {
			err := dao.db.AutoMigrate(&repo.IntranetTunnel{})
			if err != nil {
				log.Printf("auto migrate intranet tunnel table fail, err: %v", err)
			}
		} else {
			log.Println("Database connection is nil, intranet tunnel DAO will work in memory-only mode")
		}
	})

	return dao
}

// Create 创建新的隧道记录
func (it *IntranetTunnelDaoImpl) Create(tunnel *repo.IntranetTunnel) (int, error) {
	if it.db == nil {
		log.Println("Database connection is nil, skipping tunnel record creation")
		return 0, nil
	}

	var existingTunnel repo.IntranetTunnel
	err := it.db.Where("name = ? AND clientid = ? AND serverroute = ? AND clientport = ?", tunnel.Name, tunnel.ClientUUID, tunnel.ServerRoute, tunnel.ClientPort).First(&existingTunnel).Error
	if err == nil {
		tunnel.ID = existingTunnel.ID
	}
	err = it.db.Save(tunnel).Error
	return tunnel.ID, err
}

// GetByID 根据ID获取隧道
func (it *IntranetTunnelDaoImpl) GetIntranetTunnelByID(id int) (*repo.IntranetTunnel, error) {
	if it.db == nil {
		log.Println("Database connection is nil, returning empty tunnel record")
		return nil, nil
	}

	var tunnel repo.IntranetTunnel
	err := it.db.First(&tunnel, id).Error
	if err != nil {
		return nil, err
	}
	return &tunnel, nil
}

func (it *IntranetTunnelDaoImpl) GetIntranetTunnelsByIDs(ids []int) ([]*repo.IntranetTunnel, error) {
	if it.db == nil {
		log.Println("Database connection is nil, returning empty tunnel list")
		return []*repo.IntranetTunnel{}, nil
	}

	tunnels := make([]*repo.IntranetTunnel, 0)
	if len(ids) == 0 {
		return tunnels, nil
	}
	err := it.db.Where("id in (?)", ids).Find(&tunnels).Error
	return tunnels, err
}

// GetAll 获取所有隧道
func (it *IntranetTunnelDaoImpl) GetAll() ([]*repo.IntranetTunnel, error) {
	if it.db == nil {
		log.Println("Database connection is nil, returning empty tunnel list")
		return []*repo.IntranetTunnel{}, nil
	}

	var tunnels []*repo.IntranetTunnel
	err := it.db.Find(&tunnels).Error
	return tunnels, err
}

// GetEnabled 获取所有启用的隧道
func (it *IntranetTunnelDaoImpl) GetEnabled() ([]*repo.IntranetTunnel, error) {
	if it.db == nil {
		log.Println("Database connection is nil, returning empty enabled tunnel list")
		return []*repo.IntranetTunnel{}, nil
	}

	var tunnels []*repo.IntranetTunnel
	err := it.db.Where("enable = ?", true).Find(&tunnels).Error
	return tunnels, err
}

// Update 更新隧道信息
func (it *IntranetTunnelDaoImpl) Update(tunnel *repo.IntranetTunnel) error {
	if it.db == nil {
		log.Println("Database connection is nil, skipping tunnel update")
		return nil
	}

	return it.db.Save(tunnel).Error
}

// Delete 删除隧道
func (it *IntranetTunnelDaoImpl) Delete(id int) error {
	if it.db == nil {
		log.Println("Database connection is nil, skipping tunnel deletion")
		return nil
	}

	return it.db.Delete(&repo.IntranetTunnel{}, id).Error
}

// UpdateLastConnectionTime 更新最后连接时间
func (it *IntranetTunnelDaoImpl) UpdateLastConnectionTime(id int) error {
	if it.db == nil {
		log.Println("Database connection is nil, skipping last connection time update")
		return nil
	}

	sql := fmt.Sprintf("update %s set lastconnectiontime = ? where id = ?", repo.IntranetTunnelTableName)
	return it.db.Exec(sql, time.Now(), id).Error
}

func (it *IntranetTunnelDaoImpl) UpdateOnlineStatus(id int, online bool) error {
	if it.db == nil {
		log.Println("Database connection is nil, skipping online status update")
		return nil
	}

	sql := fmt.Sprintf("update %s set online = ? where id = ?", repo.IntranetTunnelTableName)
	return it.db.Exec(sql, online, id).Error
}

// GetExpiredTunnels 获取过期的隧道记录
func (it *IntranetTunnelDaoImpl) GetExpiredTunnels(expiration time.Duration) ([]*repo.IntranetTunnel, error) {
	if it.db == nil {
		log.Println("Database connection is nil, returning empty expired tunnel list")
		return []*repo.IntranetTunnel{}, nil
	}

	var tunnels []*repo.IntranetTunnel
	expiredTime := time.Now().Add(-expiration)

	// 查询未启用或不在线且最后连接时间超过过期时间的记录
	err := it.db.Where("(enable = ? OR online = ?) AND lastconnectiontime < ?",
		false, false, expiredTime).Find(&tunnels).Error

	return tunnels, err
}

// BatchDelete 批量删除隧道记录
func (it *IntranetTunnelDaoImpl) BatchDelete(ids []int) error {
	if len(ids) == 0 {
		return nil
	}
	return it.db.Where("id IN (?)", ids).Delete(&repo.IntranetTunnel{}).Error
}
