@echo off
echo Testing Windows Service Fix
echo ============================
echo.

echo Step 1: Checking if running as administrator...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)
echo OK: Running as administrator

echo.
echo Step 2: Stopping existing service (if running)...
TunnelGateway.exe -stop
echo.

echo Step 3: Uninstalling existing service (if exists)...
TunnelGateway.exe -uninstall
echo.

echo Step 4: Installing service...
TunnelGateway.exe -install
if %errorLevel% neq 0 (
    echo ERROR: Failed to install service
    pause
    exit /b 1
)
echo OK: Service installed successfully

echo.
echo Step 5: Starting service...
TunnelGateway.exe -start
if %errorLevel% neq 0 (
    echo ERROR: Failed to start service
    pause
    exit /b 1
)
echo OK: Service started successfully

echo.
echo Step 6: Checking service status...
sc query BaseOS.Network.TunnelGateway
echo.

echo Step 7: Waiting 10 seconds to see if service stays running...
timeout /t 10 /nobreak
echo.

echo Step 8: Checking service status again...
sc query BaseOS.Network.TunnelGateway
echo.

echo Test completed! 
echo If the service shows as RUNNING in both status checks, the fix is successful.
echo.
pause
